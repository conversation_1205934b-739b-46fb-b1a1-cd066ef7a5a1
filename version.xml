<project name="Plastipak_CILT" default="writebuildno" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">

	<scriptdef name="substring" language="javascript">
	     <attribute name="text" />
	     <attribute name="start" />
	     <attribute name="end" />
	     <attribute name="property" />
	     <![CDATA[
	       var text = attributes.get("text");
	       var start = attributes.get("start");
	       var end = attributes.get("end") || text.length();
	       project.setProperty(attributes.get("property"), text.substring(start, end));
	     ]]>
  	</scriptdef>

	<scriptdef name="packageversion" language="javascript">
	    <attribute name="text" />
	    <attribute name="start" />
	    <attribute name="end" />
	    <attribute name="property" />
	    <![CDATA[
			var text = attributes.get("text");
	       	var start = attributes.get("start");
	       	var end = attributes.get("end") || text.length();
	       	var newstring = text.substring(start, end);
			newstring = newstring.replace(/\./g,'~');
	       	var split = newstring.split("~");
	       	var first = +split[0];
	       	first = first.toString();
	       	var middle = +split[1];
			middle = middle.toString();
	       	var last = +split[2];
			last = last.toString();
	       	
	       	newstring = first + '.' + middle + '.' + last;
	       	project.setProperty(attributes.get("property"), newstring);
	    ]]>
  	</scriptdef>

	   <!-- Get the release number -->
    <target name="getbuildno">
        <property environment="env" />

        <!-- Now read into the build numberfile into release.str property -->
        <loadfile property="release.str"
            srcFile="BuildNo.txt" failonerror="true">
        </loadfile>

        <echo message="Using release number : ${release.str}"/>
    </target>

	<target name="updatesource" depends="getbuildno">

		<!-- Release string to be written -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<property environment="env" />

		<packageversion text="${release.str}" start="2" property="release.num" />
		<echo message="Using package version number : ${release.num}"/>
    </target>  

	<target name="writebuildno" depends="updatesource">
		<property environment="env" />

		<!-- Release string to be written -->
		<loadfile property="release.num"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>
		<echo message="Using release number : ${release.num}"/>
		
		<replace file="${basedir}/config.xml" token="99.99.99" value='${release.num}'/>
		<replace file="${basedir}/src/app/Constants/app-constants.ts" token="99.99.99" value='${release.num}'/>
		<replace file="${basedir}/src/app/Constants/app-constants.ts" token="@@RELEASE_DATE@@" value='${env.BUILD_TIMESTAMP}'/>
	</target>
</project>
