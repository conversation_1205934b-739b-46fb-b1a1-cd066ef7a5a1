{"name": "CILT", "description": "CILT Application", "version": "1.0", "INPUT_TASKLIST": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "INPUT_TASKLIST_HEADER": {"className": "com.plastipak.cilt.be.INPUT_TASKLIST_HEADER", "header": true, "field": [{"name": "ORDER_TYPE", "description": "Order Type", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "LINE", "description": "Functional Location Label", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "STR_NO", "description": "Functional Location Label", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}]}}, "REASON": {"description": "Description", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "REASON_HEADER": {"className": "com.plastipak.cilt.be.REASON_HEADER", "header": true, "field": [{"name": "CODE", "isGid": true, "length": "0", "mandatory": true, "sqlType": "TEXT"}, {"name": "DESCRP", "isGid": false, "length": "0", "mandatory": false, "sqlType": "TEXT"}]}}, "TASK_LIST": {"description": "ALM Orders: Order List from Selection", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "TASK_LIST_HEADER": {"description": "ALM Orders: Order List from Selection", "className": "com.plastipak.cilt.be.TASK_LIST_HEADER", "header": true, "field": [{"name": "ID", "description": "Order Number", "isGid": true, "length": "12", "mandatory": true, "sqlType": "TEXT"}]}, "TASK_LIST_ITEMS": {"className": "com.plastipak.cilt.be.TASK_LIST_ITEMS", "field": [{"name": "ORDERID", "description": "Order Number", "isGid": true, "length": "12", "mandatory": true, "sqlType": "TEXT"}, {"name": "ORDER_DESC", "description": "Description", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "ORDER_TYPE", "description": "Order Type", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "ORDER_TYPE_DESC", "description": "Short Text", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "LINE_NO", "description": "Functional Location Label", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "LINE_DESC", "description": "Description of functional location", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "FLOC", "description": "Functional Location Label", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "FLOC_DESC", "description": "Description of functional location", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIP", "description": "Equipment Number", "isGid": false, "length": "18", "mandatory": false, "sqlType": "TEXT"}, {"name": "EQUIP_DESC", "description": "Description of Technical Object", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "BASIC_START_DATE", "description": "Basic start date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "BASIC_FINISH_DATE", "description": "Basic finish date", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "OPERATION_NO", "description": "Operation/Activity Number", "isGid": true, "length": "4", "mandatory": true, "sqlType": "TEXT"}, {"name": "OPERATION_DESC", "description": "Operation short text", "isGid": false, "length": "40", "mandatory": false, "sqlType": "TEXT"}, {"name": "CONFIRMATION_NO", "description": "Completion confirmation number for the operation", "isGid": false, "length": "10", "mandatory": false, "sqlType": "INTEGER"}, {"name": "EXEC_START_DATE", "description": "Confirmed date for start of execution", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXEC_START_TIME", "description": "Confirmed time for 'Execution start'", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXEC_FIN_DATE", "description": "Confirmed date for execution finish", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "EXEC_FIN_TIME", "description": "Confirmed time for 'Execution finish'", "isGid": false, "length": "8", "mandatory": false, "sqlType": "TEXT"}, {"name": "SYSTEM_CONDITION", "description": "Syst.Condition", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "TASK_TYPE", "description": "User field with 20 characters", "isGid": false, "length": "20", "mandatory": false, "sqlType": "TEXT"}, {"name": "COMPLETION_STATUS", "description": "Checkbox", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}, {"name": "REASON", "description": "Reason for Variance", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "URL", "description": "Char255", "isGid": false, "length": "255", "mandatory": false, "sqlType": "TEXT"}, {"name": "SHIFT", "description": "User field with 10 characters", "isGid": false, "length": "10", "mandatory": false, "sqlType": "TEXT"}, {"name": "PRIORITY", "description": "Checkbox", "isGid": false, "length": "1", "mandatory": false, "sqlType": "TEXT"}]}}, "USER": {"description": "CILT login user details", "attachments": false, "onConflict": "SERVER_WINS", "save": true, "USER_HEADER": {"className": "com.plastipak.cilt.be.USER_HEADER", "header": true, "field": [{"name": "USER_ID", "description": "User name", "isGid": true, "length": "12", "mandatory": true, "sqlType": "TEXT"}, {"name": "FULLNAME", "description": "Full Name of Person", "isGid": false, "length": "80", "mandatory": false, "sqlType": "TEXT"}, {"name": "E_MAIL", "description": "E-Mail Address", "isGid": false, "length": "241", "mandatory": false, "sqlType": "TEXT"}, {"name": "PLANT", "description": "Plant", "isGid": false, "length": "4", "mandatory": false, "sqlType": "TEXT"}, {"name": "LANGU", "description": "2-Character SAP Language Code", "isGid": false, "length": "2", "mandatory": false, "sqlType": "TEXT"}]}}, "Index": []}