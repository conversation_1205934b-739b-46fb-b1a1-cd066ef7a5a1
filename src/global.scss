/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";


.leftContent {
  width: 50%;
  position: absolute;
  height: 100%;
  background: #00474F 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .leftGradientImage {
    // height: 100%;
    // width: 100%;
    display: block;
    margin: auto;
    // margin-left: auto;
    // margin-right: auto;
    // margin-top: 35%;
  }

  .logo {
    position: absolute;
    width: 30%;
    height: 40%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    object-fit: contain;
  }
}

.rightContent {
  width: 50%;
  height: 100%;
  right: 0;
  top: 0;
  position: absolute;
  z-index: 1;
}
.toolbar-bg {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);

  ion-icon {
    color: var(--ion-color-primary-contrast);
  }
}

.toolbar-btns {
  padding: 0 10px;
  width: 60%;
}
.text-danger{
    color: red;
}
.ngx-pagination {
  text-align: center !important;
}

.ngx-pagination .current {
  --background: var(--ion-color-primary) !important;
}
.dispaly-center {
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  font-size: 1.5em;
  color: var(--ion-color-primary) !important;
}
.ion-card {
  // background-color: #101218;
  border: 1px solid #4A4A4A;
  margin: 20px auto;
  border-radius: 10px !important;
}

.ion_input {
  color: var(--ion-text-color) !important;
  opacity: 1;
}


.download-task-modal {
    &::part(content) {
    height: 380px !important;
    width: 600px !important;
  }
}
.text-color{
  color: var(--ion-text-color) !important;
}
.ngx-pagination a {
   color: var(--ion-text-color) !important;
}
.bigdrop {
  width: 600px !important;
}
.select2-container .select2-selection--single{
  height:45px !important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 40px !important;
}
.select2-container--default .select2-selection--single .select2-selection__arrow{
  height: 40px !important;

}
.select2-container--default .select2-selection--single .select2-selection__clear
{
  height: 35px !important;
}

 