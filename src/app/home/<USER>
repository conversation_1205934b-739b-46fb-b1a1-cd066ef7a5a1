
.download-button{
    outline: 1px solid white;
    margin-right: 20px;
    border-radius: 5px;
     background-color: var(--ion-color-primary);
     color: #fff !important;
}
// .tabBtn:active{
//   background: #051e3b;
//   border-radius: 8px;
//   height: 26px;
// }

.tabBtn{
  border-radius: 8px;
}
ion-button:active{
  .tabBtn{
     background: #051e3b;
     border-radius: 8px;
     height: 26px;
  }
}

.segment-active {
  background-color: var(--ion-color-primary);
  color: #fff !important;
  --color-hover: #fff;
}

.segment-active:hover {
  color: #fff !important;
}

ion-segment {
  ion-segment-button {
    border: 1px solid white;
    border-radius: 10px 10px 10px 10px;
    height: 45px;
    --background-checked: var(--ion-color-primary);
    min-height: 15px !important;
    text-transform: none;
    color: white;
    --color-checked: transparent;
    --color-focused: transparent;
    --indicator-color: transparent;
    --indicator-color: transparent;
    margin-top: 5px;
  }

  .segment-active:hover {
    color: #fff;

    ion-label {
      color: #fff;
    }
  }

  .segment-deactive {
    background-color: #D6E4E5 !important;
    color: var(--ion-color-primary);
  }

  .segment-deactive:hover {
    background-color: #D6E4E5 !important;
    color: var(--ion-color-primary);
  }
}

.header-card {
  background-color: rgb(235, 235, 235);
  font-size: 14px;
  border-width: 1px;
  font-weight: bold;
  font-style: normal;
  opacity: 1;
  margin: 0px;
  border-radius: 0px !important;
  color: rgb(63, 63, 63);
}

.header-card-details {
  font-size: 14px;
  border-width: 1px;
  font-style: normal;
  opacity: 1;
  margin: 0px;
  border-radius: 0px !important;
  color: rgb(65, 65, 65);
  border-bottom: 1px solid lightgray;
 
}
.cilt-img{
  height: 50px;
  width: 55px;
}
.header-align{
  // text-align: center;
  margin-left: 10px;
}
.row-disable{
  pointer-events:none !important;
  
}
.row-enable{
   pointer-events:auto !important;
  //  background-color: white;
}

ion-button {
  text-transform: none;
}
.footer-btn{
  --background: var(--ion-color-primary) !important;
  --color: var(--ion-color-primary-contrast) !important;
      border: 1px solid;
      border-radius: 5px;
      margin-right: 10px;
}

.errMsg{
  color: red;
  padding: 5px;
  margin-left: 25px;
}
ion-icon{
  font-size: 35px;
  margin-bottom: 3px;
  padding: 5px;
}
.row-priority{
  background: #F9F981 !important;
}

.shift-style {
  text-transform: lowercase;
  display: inline-block;
  text-align: center;
}

.shift-style:first-letter {
  text-transform: uppercase
}

.button-size{
  margin-left: 5px;
  margin-top: 7px;
  margin-right: 5px;
  height: 25px !important;
  width: 25px !important;
}

.search-float-right{
  float: right !important;
}
.logout-button{
  margin-left: 100px !important;
}

ion-icon {
  pointer-events: none;
}

.background-color{
  background-color: white !important;
}

.radio-button-margin{
  margin-bottom: -20px;
  margin-top: -20px;
}

.header-title{
  display:flex;
  justify-content: space-around;
}
.user-title{
  float: center;
  font-size: 16px;
  font-weight:100;
}

.notification-dot {
  position: absolute;
  top: 11px;
  right: -10px;
  width: 20px;
  height: 20px;
  background-color: red;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}