import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HomePage } from './home.page';

import { HomePageRoutingModule } from './home-routing.module';
import { NgxPaginationModule } from 'ngx-pagination';
import { DownloadTaskPage } from '../download-task/download-task.page';
import { DateformatPipe } from '../dateformat.pipe';
import { IonicSelectableModule } from 'ionic-selectable';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ChipModule } from 'primeng/chip';
import { ButtonModule } from 'primeng/button';
import { RadioButtonModule } from 'primeng/radiobutton';
import { BadgeModule } from 'primeng/badge';
import { DropdownModule } from 'primeng/dropdown';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    HomePageRoutingModule,
    NgxPaginationModule,
    ReactiveFormsModule,
    IonicSelectableModule,
    TableModule,
    InputTextModule,
    ChipModule,
    ButtonModule,
    RadioButtonModule,
    BadgeModule,
    DropdownModule
  ],
  entryComponents: [DownloadTaskPage],
  declarations: [HomePage, DownloadTaskPage, DateformatPipe]
})
export class HomePageModule { }
