import { Injectable, NgZone } from '@angular/core';
import {
  RequestType,
  ResultType,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AlertController, ModalController, ToastController, LoadingController } from '@ionic/angular';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { NavigationExtras, Router } from '@angular/router';
import { AppConstants } from '../Constants/app-constants';
import { Idle, DEFAULT_INTERRUPTSOURCES } from '@ng-idle/core';

//creating a global variable
declare global {
  var loginFlag: boolean;
}
@Injectable({
  providedIn: 'root'
})

export class DataService {
  public isLogout = new BehaviorSubject(false);
  public isNetworkConnected = false;

  private idleState: string = '';
  private timedOut: boolean = false;
  private idleSubscriptions: Subscription = new Subscription();
  private loading: HTMLIonLoadingElement | null = null;

  constructor(
    public unviredSDK: UnviredCordovaSDK,
    public alertController: AlertController,
    public router: Router,
    public ngZone: NgZone,
    private toastController: ToastController,
    private idle: Idle,
    private modalController: ModalController,
    public loadingController: LoadingController

  ) {
    this.isNetworkConnected = navigator.onLine;
    window.addEventListener('offline', () => {
      this.isNetworkConnected = false;
    });

    window.addEventListener('online', () => {
      this.isNetworkConnected = true;
    });
    globalThis.loginFlag = false;
  }

  initializeIdleWatcher(idleTime: any, timeoutPeriod: any) {
    // Set idle time and timeout period
    this.idle.setIdle(idleTime); // Idle time in seconds
    this.idle.setTimeout(1); // Timeout period in seconds
    this.idle.setInterrupts(DEFAULT_INTERRUPTSOURCES);

    // Unsubscribe from previous subscriptions to prevent duplicates
    this.idleSubscriptions.unsubscribe();
    this.idleSubscriptions = new Subscription();

    // Subscribe to idle events
    this.idleSubscriptions.add(
      this.idle.onIdleEnd.subscribe(() => {
        this.idleState = 'No longer idle.';
        this.resetIdleWatcher();
      })
    );

    this.idleSubscriptions.add(
      this.idle.onTimeout.subscribe(async () => {
        this.idleState = 'Timed out!';
        this.timedOut = true;
        let hidden = false
        if (document.visibilityState === "hidden") {
          console.log('Browser is minimized or the tab is inactive.');
          this.stopIdleWatcher();
          hidden = true
          console.log('Logged out due to inactivity');
          await this.logout();
          this.sessionExpiredAlert()
          await this.modalController.dismiss()
        } else if (document.visibilityState === "visible") {
          console.log("Browser is active and the tab is visible.");
          setTimeout(async () => {
            if (!this.idleState.includes('Idle stopped.')) {
              this.stopIdleWatcher();
              alert.dismiss();
              console.log('Logged out due to inactivity');
              await this.logout();
              this.sessionExpiredAlert()
              await this.modalController.dismiss()
            }
          }, timeoutPeriod * 1000); // Adjust logout time dynamically
        }

        const alert = await this.alertController.create({
          header: 'Session Expiring Soon',
          message: 'Your session is about to expire due to inactivity. To continue, please click Extend Session',
          buttons: [
            {
              text: 'Extend Session',
              role: 'cancel',
              handler: () => {
                this.resetIdleWatcher();
                this.idleState = 'Idle stopped.';
                alert.dismiss();
              },
            },
          ],
          backdropDismiss: false,
        });
        await alert.present();

        if (hidden) {
          await alert.dismiss();
        }
      })
    );

    this.idleSubscriptions.add(
      this.idle.onIdleStart.subscribe(() => {
        this.idleState = 'You’ve gone idle!';
      })
    );

    this.idleSubscriptions.add(
      this.idle.onTimeoutWarning.subscribe((countdown) => {
        this.idleState = 'You will time out in ' + countdown + ' seconds!';
      })
    );

    // Start idle monitoring
    this.resetIdleWatcher();
  }

  resetIdleWatcher() {
    this.idle.watch();
    this.timedOut = false;
  }

  stopIdleWatcher() {
    this.idle.stop();
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.idleSubscriptions.unsubscribe();
  }

  async sessionExpiredAlert() {
    const alert = await this.alertController.create({
      header: 'Session Expired',
      message: 'Your session has expired due to inactivity.',
      buttons: [
        {
          text: 'Login',
          handler: () => {
          }
        }
      ]
    });

    await alert.present();
  }

  async logout() {
    let queryData = localStorage.getItem('querydata');
    localStorage.clear();
    await this.unviredSDK.logout();
    this.ngZone.run(() => {
      // const dataToSend: NavigationExtras = {
      //   queryParams: {
      //     type: 0,
      //   },
      // };
      localStorage.setItem('querydata', queryData);
      localStorage.setItem('resultType', '0');
      this.router.navigate(['login']);
    });
  }

  getLogoutStatus(): Observable<boolean> {
    return this.isLogout.asObservable();
  }

  clearData() {
    this.unviredSDK.logout();
  }

  checkStringAndConvertIntoNumber(data: any): number {
    let num: number;
    switch (true) {
      case typeof data === 'string':
        num = +data;
        break;
      case typeof data === 'number':
        num = data;
        break;
    }
    return num;
  }

  async displayAlert(msg: string) {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: msg,
      backdropDismiss: false,
      buttons: [{ text: 'Ok' }],
    });
    await alert.present();
  }

  // Get Data from Local DB
  async getData(headerName: string, whereCondition?: any) {
    if (whereCondition) {
      var result = await this.unviredSDK.dbExecuteStatement(
        `SELECT * FROM ${headerName} WHERE ` + whereCondition
      );
    } else {
      var result = await this.unviredSDK.dbExecuteStatement(
        `SELECT * FROM ${headerName}`
      );
    }
    if (result.type == ResultType.success) {
      return result.data;
    } else {
      this.unviredSDK.logError(
        'dataService',
        'getData',
        `Error while fetching ${headerName} Data - ${result.message}`
      );
    }
  }
  async getMasterData() {
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,
        '',
        '',
        AppConstants.PA_GET_MASTERDATA,
        true
      );
      if (result.type == ResultType.success) {
        // 
        this.unviredSDK.logInfo(
          'DataService',
          'getMasterData',
          'Master data has retrived successfully.'
        );
      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'getMasterData',
          'Error occured while getting Master data'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getMasterData',
          'Error occured while getting Master data - ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getMasterData',
        'Error occured while getting Master data - ' + error
      );
      return;
    }
  }

  handleInfoMessage(result) {
    let infoMessage = '';
    if (
      result &&
      result.data &&
      result.data.InfoMessage &&
      result.data.InfoMessage.length > 0
    ) {
      for (var info of result.data.InfoMessage) {
        if (info.category != 'SUCCESS') {
          if (infoMessage) infoMessage = infoMessage + '\n';
          infoMessage = infoMessage + info.message;
        }
      }
    }
    return infoMessage;
  }

  async getTaskList(data: any) {
    try {
      let customData =
      {
        INPUT_TASKLIST: [
          {
            INPUT_TASKLIST_HEADER: {
              ORDER_TYPE: "CILT",
              LINE: data.LineNumber,
              STR_NO: "",
              PLANT: data.PlantNumber
            }
          }
        ]
      }
      let result = await this.unviredSDK.syncForeground(RequestType.PULL, '', customData, AppConstants.PA_GET_TASKLIST, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'getTaskList',
          'TaskList'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'getTaskList',
          'Error while getting task list data.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getTaskList',
          'Error while getting task list data:' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getTaskList',
        'getTaskList' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }
  async upadteTaskList(customData: any) {
    try {
      let result = await this.unviredSDK.syncForeground(RequestType.PULL, '', customData, AppConstants.PA_UPDATE_TASKS, true);
      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'DataService',
          'upadteTaskList',
          'upadte TaskList'
        );

      } else if (result.code && (result.code === 401 || result.code === 404)) {
        this.unviredSDK.logError(
          'DataService',
          'upadteTaskList',
          'Error while upadte TaskList.'
        );
        await this.logout();
      } else {
        this.unviredSDK.logError(
          'DataService',
          'upadteTaskList',
          'Error while upadteTaskList:' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'upadteTaskList',
        'Error while upadteTaskList ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Toast for 2 sec to show data is proccessing in background
  async showToast(msg: string) {
    const toast = await this.toastController.create({
      message: msg,
      animated: true,
      color: 'primary',
      duration: 3000,
      position: 'middle'
    });
    toast.present();
  }

  getUMPUrl(): string {
    let hosturl = location.protocol + "//" + location.hostname;
    console.log("HOST URL____", hosturl);
    if (hosturl.toLocaleLowerCase().includes("plastipak.com") || hosturl.toLocaleLowerCase().includes("plastipak.eu")) {
      hosturl = 'https://ump.plastipak.com/UMP'
    } else {
      hosturl = 'https://absomi01.absopure.com:8443/UMP/'
      // hosturl = 'https://ump.plastipak.com/UMP'
    }
    return hosturl;
  }

  getAUTHUrl(): string {
    let hosturl = location.protocol + "//" + location.hostname;
    console.log("HOST URL____", hosturl);
    if (hosturl.toLocaleLowerCase().includes("plastipak.com") || hosturl.toLocaleLowerCase().includes("plastipak.eu")) {
      hosturl = 'https://ump.plastipak.com/AUTH/login'
    } else {
      hosturl = 'https://absomi01.absopure.com:8443/AUTH/login'
    }
    return hosturl;
  }

  // Display loader
  async pleaseWaitLoader() {
    console.log("pleaseWaitLoader() called");
    if (!this.loading) {
      this.loading = await this.loadingController.create({
        message: 'Please wait...',
        mode: 'ios',
        backdropDismiss: false,
      });
      await this.loading.present();
    }
  }

  async dismissLoadingController() {
    console.log("dismissLoadingController() called");
    if (this.loading) {
      await this.loading.dismiss();
      this.loading = null;
    }
  }

}
