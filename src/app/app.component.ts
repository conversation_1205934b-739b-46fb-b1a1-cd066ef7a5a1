import { Component, HostListener, NgZone, OnInit } from '@angular/core';
import { NavController, Platform } from '@ionic/angular';
import {
  UnviredCordovaSDK,
  LoginParameters,
  LoginType,
  LoginListenerType,
  LoginResult,
  UnviredResult,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from './Services/data.service';
import { ActivatedRoute, NavigationExtras, NavigationStart, Router } from '@angular/router';
import { AppConstants } from './Constants/app-constants';
import { Subscription } from 'rxjs';


@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})

export class AppComponent implements OnInit {
  idleTime: number = 1680

  constructor(
    public platform: Platform,
    public unviredSdk: UnviredCordovaSDK,
    public dataService: DataService,
    private router: Router,
    private ngZone: NgZone,
    private navCtrl: NavController,
    private route: ActivatedRoute,

  ) {
    this.checkScreenTime()
    this.platform.ready().then(async () => {
      await this.initializeApp();
      this.dataService.initializeIdleWatcher(this.idleTime, 120);
      this.dataService.getLogoutStatus().subscribe(async (status: boolean) => {
        if (status) {
          localStorage.clear();
          this.dataService.clearData();
          this.ngZone.run(() => {
            const dataToSend: NavigationExtras = {
              queryParams: {
                type: 0,
              },
            };
            this.navCtrl.navigateBack(['login'], dataToSend);
          });
        }
      });
    });
  }

  ngOnInit() {
  }

  checkScreenTime() {
    const expiryTime = parseInt(localStorage.getItem('ng2Idle.main.expiry'), 10);
    const currentTime = Date.now();
    const timeDifference = currentTime - expiryTime;
    if (timeDifference > 0) {
      this.dataService.logout();
    }
  }

  async initializeApp() {
    let loginParameters = new LoginParameters();
    loginParameters.appName = 'CILT';
    loginParameters.url = this.dataService.getUMPUrl();
    loginParameters.company = 'PLASTIPAK';
    loginParameters.loginType = LoginType.unvired;
    loginParameters.domain = 'PLASTIPAK';
    loginParameters.jwtOptions = { app: 'cilt' };
    loginParameters.metadataPath = 'assets/metadata.json';

    let result: LoginResult;
    try {
      result = await this.unviredSdk.login(loginParameters);
      this.route.queryParams.subscribe(params => {
        console.log("params" + JSON.stringify(params));
        if (params && params.plant && params.line) {
          let inputData = { LineNumber: params.line, PlantNumber: params.plant };
          localStorage.setItem('querydata', JSON.stringify(inputData))
        }
      });

      localStorage.setItem('resultType', String(result.type))
      switch (result.type) {
        case LoginListenerType.auth_activation_required:
          this.ngZone.run(() => this.router.navigate(['login']));
          break;
        case LoginListenerType.app_requires_login:
          this.ngZone.run(() => this.router.navigate(['login']));
          break;
        case LoginListenerType.login_success:
          let tempDetails = localStorage.getItem('userDetails');
          if (tempDetails != null && tempDetails != null) {
            let userDetails = JSON.parse(atob(tempDetails));
            await this.unviredSdk.setClientCredentials(userDetails);
            console.log("Print time");
            this.ngZone.run(() => this.router.navigate(['home']));
            let unload = localStorage.getItem('unload');
            let load = localStorage.getItem('load');
          } else {
            localStorage.clear();
            this.unviredSdk.logout();
            this.ngZone.run(() => this.router.navigate(['login']));
          }
          break;
      }
    } catch (error) {
      this.unviredSdk.logError(
        'AppComponent',
        'initializeApp',
        'ERROR: ' + error
      );
    }
    this.platform.backButton.subscribeWithPriority(10, () => {
      console.log('Handler was called!');
    });
  }
}
