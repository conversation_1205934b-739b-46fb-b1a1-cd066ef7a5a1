import { Component, NgZone, OnInit } from '@angular/core';
import { LoadingController } from '@ionic/angular';
import { TASK_LIST_HEADER } from '../Constants/HEADER';
import * as moment from 'moment';
import {
  AuthenticateActivateResult,
  AuthenticateAndActivateResultType,
  LoginParameters,
  LoginType,
  ResultType,
  UnviredCordovaSDK,
  UnviredCredential,
  UnviredResult,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from '../Services/data.service';
import { AppConstants } from '../Constants/app-constants';
import { ActivatedRoute, Router } from '@angular/router';
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { HttpClient } from '@angular/common/http';
@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {

  public username: string;
  public password: string;
  public validateUsername: boolean = true;
  public validatePassword: boolean = true;
  public showErrorMsg: boolean = false;
  public forgotPasswordSuccMsg: boolean = false;
  public password_type: string = 'password';
  public versionHide: boolean = true;
  public loginErrMsg: string = '';
  public appVersion: string = '';
  public errorMessage: string;
  loginForm: FormGroup;
  isSubmitted = false;
  public queryparams;
  public reasonCodeList = [];
  public funLocList = [];
  public isTaskDataLoaded: boolean = false;
  public taskList: TASK_LIST_HEADER[] = [];
  public originalTaskList: any[];
  public selectedDate: string;
  responseToken: any;


  constructor(
    private ngZone: NgZone,
    public loadingController: LoadingController,
    public unviredSdk: UnviredCordovaSDK,
    public dataService: DataService,
    public router: Router,
    private route: ActivatedRoute,
    public formBuilder: FormBuilder,
    private http: HttpClient,

  ) {

    this.loginForm = this.formBuilder.group({
      userName: ['', [Validators.required]],
      pwd: ['', [Validators.required]]
    })
  }
  
  ngOnInit() {
  }

  removeDuplicateObjectFromArray(array, key) {
    var check = new Set();
    return array.filter(obj => !check.has(obj[key]) && check.add(obj[key]));
  }

  async ionViewWillEnter() {
      this.loginForm.reset();
      this.isSubmitted = false;
      await this.loadData();
    
  }

  loginKeyEvent(e: any) {
    if (e.key == 'Enter') {
      this.login();
    }
  }

  togglePasswordMode() {
    this.password_type = this.password_type === 'text' ? 'password' : 'text';
  }

  async loadData() {
    await this.pleaseWaitLoader();
    try {
      let reasonHeaderData = await this.dataService.getData('REASON_HEADER');
      if (reasonHeaderData.length > 0) {
        this.reasonCodeList = reasonHeaderData;
      } else {
        console.log("no data found");
      }

      let taskListdata = await this.dataService.getData('TASK_LIST_ITEMS');
      if (taskListdata.length > 0) {
        this.funLocList = [];
        for (let i = 0; i < taskListdata.length; i++) {
          taskListdata[i].isCustomMark = false;
          taskListdata[i].isChanged = false;
          taskListdata[i].errMsg = "";
          taskListdata[i].completion = taskListdata[i].COMPLETION_STATUS;

          if (taskListdata[i].REASON && taskListdata[i].REASON.length > 0 && taskListdata[i].COMPLETION_STATUS !== 'X') {
            taskListdata[i].COMPLETION_STATUS = 'false';
          }
          if (taskListdata[i].REASON === null) {
            taskListdata[i].REASON = ""
          }
          taskListdata[i].sel = taskListdata[i].REASON;
          let Obj = { FLOC: taskListdata[i].FLOC, FLOC_DESC: taskListdata[i].FLOC_DESC }
          this.funLocList.push(Obj);
          this.funLocList = this.removeDuplicateObjectFromArray(this.funLocList, 'FLOC')
        }
      } else {
        console.log("no data found");
      }

      this.isTaskDataLoaded = true;
      this.taskList = taskListdata;
      this.originalTaskList = this.taskList;
      const format = "MMM Do YYYY"
      this.selectedDate = moment().format('MMM Do YYYY');
      let seldateTasks = this.taskList.filter(
        (item) =>
          ((moment.utc(item.BASIC_START_DATE, 'YYYY-MM-DD').format(format) === this.selectedDate) && item.COMPLETION_STATUS !== 'X')
      );
      this.taskList = seldateTasks;
    } finally {
      await this.dismissLoadingController();
    }
  }

  // User Login
  async login() {
    this.isSubmitted = true;
    if (this.loginForm.valid) {
      this.unviredSdk.logDebug('LoginPage', 'login', 'Processing user login');
      await this.pleaseWaitLoader();
      try {
        this.errorMessage = '';
        if (!this.dataService.isNetworkConnected) {
          this.unviredSdk.logError(
            'LoginPage',
            'login',
            'Please check internet connection and try again!'
          );
          this.errorMessage = 'Please check internet connection and try again!';
          return;
        }

        function convertToBase64(data: any): string { return btoa(data); }
        let rawJsonString = { USERID: this.username.trim(), PSWD: this.password.trim(), PORT : AppConstants.SAP_PORT };
        const raw = convertToBase64(JSON.stringify(rawJsonString));

        try {
          this.responseToken = await this.http.post<any>(this.dataService.getAUTHUrl(), raw, { responseType: 'json' }).toPromise();
          this.errorMessage = '';
          let loginParameters = new LoginParameters();
          loginParameters.appName = 'CILT';
          loginParameters.url = this.dataService.getUMPUrl();
          loginParameters.company = 'PLASTIPAK';
          loginParameters.username = 'UNVIRED';
          loginParameters.loginType = LoginType.saml2;
          loginParameters.domain = 'PLASTIPAK';
          loginParameters.jwtOptions = { app: 'cilt' };
          loginParameters.metadataPath = 'assets/metadata.json';
          loginParameters['samlToken'] = this.responseToken.token;

          this.unviredSdk.logDebug(
            'LoginPage',
            'login',
            'Calling Authenticate And Activate'
          );
          let authenticateActivateResult: AuthenticateActivateResult;
          try {
            authenticateActivateResult =
              await this.unviredSdk.authenticateAndActivate(loginParameters);
            if (
              authenticateActivateResult.type ===
              AuthenticateAndActivateResultType.auth_activation_success
            ) {
              this.unviredSdk.logDebug(
                'LoginPage',
                'login',
                'Authenticate And Activation Success'
              );

              this.saveDataToLocalStorage();
              globalThis.loginFlag = true;
              let credentials: UnviredCredential[] = [];
              let isCredentialsRequriedResult: UnviredResult =
                await this.unviredSdk.isClientCredentialsSet();
              if (
                isCredentialsRequriedResult &&
                isCredentialsRequriedResult.data === false
              ) {
                localStorage.setItem("userName", this.username);
                credentials = [
                  {
                    user: this.username.trim(),
                    password: this.password.trim(),
                    port: AppConstants.SAP_PORT,
                  },
                ];
                await this.unviredSdk.setClientCredentials(credentials);
                localStorage.setItem(
                  'userDetails',
                  btoa(JSON.stringify(credentials))
                );
              }

              let inputData = JSON.parse(localStorage.getItem('querydata'));
              if (inputData) {
                let isCredentialsRequriedResult: UnviredResult = await this.unviredSdk.isClientCredentialsSet();
                if (isCredentialsRequriedResult && isCredentialsRequriedResult.data === false) {
                  let tempDetails = localStorage.getItem('userDetails');

                  if (tempDetails != null && tempDetails != null) {
                    let userDetails = JSON.parse(atob(tempDetails));
                    await this.unviredSdk.setClientCredentials(userDetails)
                  }
                }
                let checkUserId: UnviredResult = await this.unviredSdk.isClientCredentialsSet();
                if (checkUserId && checkUserId.data === false) {
                  this.dataService.logout();
                  this.errorMessage = "Something went wrong while login, please try again!";
                }
                let masterdata = await this.dataService.getMasterData();
                if (masterdata.type == ResultType.error) {
                  this.dataService.logout();
                  this.errorMessage = masterdata.message;
                } else {
                  let taskListres: any = await this.dataService.getTaskList(inputData);
                  if (taskListres.type == 0) {
                    this.ngZone.run(() => this.router.navigate(['home']));
                  } else {
                    if (taskListres.message) {
                      this.errorMessage = taskListres.message;
                    }
                  }
                }
              } else {
                this.dataService.logout();
                this.errorMessage = "Please provide Line Number and Plant Number as part of query parameter in Url.";
              }
            } else if (
              authenticateActivateResult.type ===
              AuthenticateAndActivateResultType.auth_activation_error
            ) {
              this.unviredSdk.logError(
                'LoginPage',
                'login',
                'Auth Activation Error'
              );
              let authErrorMsg = authenticateActivateResult.error;
              if (authErrorMsg && authErrorMsg.length > 0) {
                await this.dismissLoadingController();
                this.dataService.displayAlert(authErrorMsg);
              }
            }
          } catch (error) {
            this.unviredSdk.logError(
              'LoginPage',
              'login',
              'Error in calling Authenticate And Activate'
            );
            this.errorMessage = error;
          }
        } catch (error) {
          console.error('Error occurred:', error);
          await this.dismissLoadingController();
          this.dataService.displayAlert(error.error.error);
          this.errorMessage = error.error.error;
        }
      } finally {
        await this.dismissLoadingController();
      }
    } else {
      console.log("login form is invalid")
    }
  }

  saveDataToLocalStorage() {
    localStorage.setItem('url', this.dataService.getUMPUrl());
  }

  get errorControl() {
    return this.loginForm.controls;
  }

  // Display loader
  async pleaseWaitLoader() {
    await this.dataService.pleaseWaitLoader();
  }

  async dismissLoadingController() {
    await this.dataService.dismissLoadingController()
  }
}
