<!-- <ion-header>
  <ion-toolbar>
    <ion-title>login</ion-title>
  </ion-toolbar>
</ion-header> -->

<ion-content class="ion-padding">
  <div class="login-background-container">
    <div class="container container-div">

      <div class="leftContent">
        <img class="leftGradientImage" src="assets/Images/loginBG.png">
        <!-- <img class="logo" src="assets/Images/logo.png"> -->
        <div class="top-right">
          <div class="header">CILT Maintenance Portal
          </div><br>
          <span style="color: white;">Task Management</span>
        </div>
      </div>

      <div class="rightContent">
        <!-- <ion-progress-bar type="indeterminate"></ion-progress-bar> -->

        <div class="inputFields">
          <form [formGroup]="loginForm" (ngSubmit)="login()" style="padding: 20px">
            <div class="login-label">Welcome</div>
            <div>
              <ion-item class="ion-card" lines="none">
                <ion-label position="floating" style="color: black;">SAP User Id</ion-label>
                <ion-input type="text" inputmode="text" class="ion_input" style="color: black !important;"
                  [(ngModel)]="username" name="username" (ionBlur)="versionHide = true" formControlName="userName"
                  autocapitalize="off">
                </ion-input>
              </ion-item>
              <!-- <div *ngIf="userId.errors && (userId.dirty || userId.touched)" style="margin-top: 2px">
              <small *ngIf="userId.errors.required" class="text-danger">User Name is required.</small>
            </div> -->
              <span class="error" *ngIf="isSubmitted && errorControl.userName.errors?.required">
                SAP User Id is required.
              </span>
              <!-- <small *ngIf="!validateUsername" class="text-danger">User Name is required.</small> -->
            </div>
            <div>
              <ion-item class="ion-card" lines="none">
                <ion-label position="floating" style="color: black;">Password</ion-label>
                <ion-input [type]="password_type" inputmode="password" class="ion_input"
                  style="color: black !important;" [(ngModel)]="password" name="password"
                  (ionFocus)="versionHide = true" (ionBlur)="versionHide = true" (keyup)="loginKeyEvent($event)"
                  formControlName="pwd"></ion-input>
                <span slot="end" (click)="togglePasswordMode()" class="password-field">
                  <!-- <i *ngIf="password && password_type == 'text'" class="fa fa-eye" aria-hidden="true"></i> -->
                  <ion-icon *ngIf="password && password_type == 'text'" name="eye-outline"></ion-icon>
                  <ion-icon *ngIf="password && password_type == 'password'" name="eye-off-outline"></ion-icon>
                  <!-- <i *ngIf="password && password_type == 'password'" class="fa fa-eye-slash"  aria-hidden="true"></i> -->
                </span>
              </ion-item>
              <!-- <div *ngIf="pwd.errors && (pwd.dirty || pwd.touched)" style="margin-top: 2px">
              <small *ngIf="pwd.errors.required" class="text-danger">Password is required.</small>
            </div> -->
              <!-- <small *ngIf="!validatePassword" class="text-danger">password is required</small> -->
              <span class="error" *ngIf="isSubmitted && errorControl.pwd.errors?.required">
                Password is required.
              </span>

            </div>
            <ion-button class="ionLoginBtn" type="button" fill="clear" expand="block"
              (click)="login()">Login</ion-button>
          </form>
          <div style="margin-left: 20px !important; margin-top: 40px">
            <small class="text-danger" style="font-size: 17px">{{errorMessage}}</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</ion-content>