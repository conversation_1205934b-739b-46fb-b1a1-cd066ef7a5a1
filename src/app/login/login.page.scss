.login-background-container {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  width: 100%;
  height: 100vh;
  background-color: #d3d3d3;
}

.container {
  /* Must manually set width/height */
  width: 1400px;
  height: 750px;

  /* The magic centering code */
  margin: auto;
  position: absolute;
  top: 0;
  bottom: 0; /* Aligns Vertically - Remove for Horizontal Only */
  left: 0;
  right: 0; /* Aligns Horizontally - Remove for Vertical Only  */

  /* Prevent div from overflowing main window */
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;

  background: #fff;
  border-radius: 5px;
  -webkit-box-shadow: 4px 4px 20px -1px rgba(59, 59, 59, 0.6);
  -moz-box-shadow: 4px 4px 20px -1px rgba(59, 59, 59, 0.6);
  box-shadow: 4px 4px 20px -1px rgba(59, 59, 59, 0.6);
}

.container:before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 66%;
  top: 0%;
  bottom: 0%;
//   background: url("/DYMS/assets/img/LoginBG.png");
  transform: skewX(50deg) rotate(0deg);
}

.inputFields {
  position: relative;
  top: 25%;
  left: 50%;
  transform: translate(-50%, 0%);
  width: 90%;
}

.login-label {
  color: black;
  font-size: 24px;
  text-align: center;
}
ion-item.ion-invalid {
  --highlight-background: var(--ion-color-danger) !important;
}
ion-item{
    --background: transparent;
    --highlight-background: #2586c7;
}

.password-field {
//   color: #404040;
//   float: right;
  margin-top: 22px;
//   margin-right: 8px;
//   position: relative;
//   z-index: 2;
  cursor: pointer;
  color: black;
}

.errMsgs {
  display: flex;
  justify-content: space-between;
}
text-danger{
    color: #f44336
};

.error-msg {
    margin-top: 5px;
    color: #f44336;
    max-width: 100%;
    box-sizing: border-box;
    word-break: break-word;
  }

  .successMsg {
    margin-top: 5px;
    color: #34a325;
    max-width: 100%;
    box-sizing: border-box;
    word-break: break-word;
  }

  .ionLoginBtn {
  position: absolute;
  color: white;
  left: 50%;
  transform: translate(-50%, 0%);
  background: linear-gradient(to right, #1ac4f8, #2586c7);
  --background: linear-gradient(to right, #1ac4f8, #2586c7);
  width: 94%;
  border-radius: 14px;
  // margin-top: 5%;
  box-shadow: 1px 0px 1px !important;
}

.cmpLogo {
  width: 20%;
}
.forgotPassword{
    text-align: end;
}
.top-right {
  position: absolute;
  top: 10px;
  left:10px;
//   right: 16px;
  color: black;
//   text-align: right;
  padding: 5px; 
//   font-weight: bold;
}

.header {
  font-size: 24px;
  color: #fff;
  margin: 15px 20px 0px 0px;
}
input:focus{
    outline: none;
}
.error {
  color: red;
}