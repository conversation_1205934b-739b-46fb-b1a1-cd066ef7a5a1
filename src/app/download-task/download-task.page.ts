import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-download-task',
  templateUrl: './download-task.page.html',
  styleUrls: ['./download-task.page.scss'],
})
export class DownloadTaskPage implements OnInit {
 
  ionicForm: FormGroup;
  isSubmitted = false;
  constructor(
    public formBuilder: FormBuilder,
    private modalController: ModalController
  ) {

   }

  ngOnInit() {
    this.ionicForm = this.formBuilder.group({
      PlantNumber: ['', [Validators.required]],
      LineNumber: ['', [Validators.required]]
    })
  }

  get errorControl() {
    return this.ionicForm.controls;
  }

  submitForm() {
    this.isSubmitted = true;
    if (!this.ionicForm.valid) {
      console.log('Please provide all the required values!')
      // return false;
    } else {
      console.log(this.ionicForm.value)
      this.modalController.dismiss(this.ionicForm.value);

    }
  }
  cancel(){
    this.modalController.dismiss();
  }

}
