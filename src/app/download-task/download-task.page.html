<ion-header>
  <ion-toolbar class="toolbar-bg">
    <ion-title>Selection Screen</ion-title>
  <ion-buttons slot="end" >
    <ion-button (click)="cancel()" style="margin-right: 10px;">
      <ion-icon name="close" slot="icon-only"></ion-icon>
    </ion-button>
  </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content>
  <form [formGroup]="ionicForm" (ngSubmit)="submitForm()" style="position: relative;">

    <ion-item lines="none" class="ion-item" style="width: 90%;">
      <ion-label position="floating" class="text-color">Plant</ion-label>
      <ion-input formControlName="PlantNumber" type="text" class="ion_input"></ion-input>
    </ion-item>
    <span class="error ion-padding" *ngIf="isSubmitted && errorControl.PlantNumber.errors?.required">
      Plant Number is required.
    </span>

    <ion-item lines="none" class="ion-item" style="width: 90%;">
      <ion-label position="floating" class="text-color">Line</ion-label>
      <ion-input formControlName="LineNumber" type="text" class="ion_input"></ion-input>
    </ion-item>
    <span class="error ion-padding" *ngIf="isSubmitted && errorControl.LineNumber.errors?.required">
      Line Number is required.
    </span>

    <!-- <ion-row>
      <ion-col>
        <ion-button type="submit">Submit</ion-button>
      </ion-col>
      <ion-col>
        <ion-button type="cancel">Cancel</ion-button>
      </ion-col>
    </ion-row> -->
    <!-- <ion-buttons slot="end"> -->
    <div style="padding: 20px;text-align: right;margin-right: 5px;">
      <ion-button type="submit" slot="end" (click)="submitForm()" style="text-transform: none;">Submit
      </ion-button>
      <!-- <ion-button type="cancel" slot="end" (click)="cancel()">Cancel
      </ion-button> -->
    </div>
    <!-- </ion-buttons> -->
  </form>

</ion-content>
