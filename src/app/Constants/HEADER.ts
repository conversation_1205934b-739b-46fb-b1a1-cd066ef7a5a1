export class DATA_STRUCTURE {
    LID:                   string;
    FID:                   string;
    OBJECT_STATUS:         number = 1;
    SYNC_STATUS:           number = 0;
}

export class INPUT_TASKLIST_HEADER extends DATA_STRUCTURE {
    ORDER_TYPE:               string;
    LINE:                 string;
    STR_NO:         string;
    PLANT:                 string;
}
export class REASON_HEADER extends DATA_STRUCTURE {
    CODE: string;
    DESCRP: string;
    STR_NO: string;
    PLANT: string;
}
export class TASK_LIST_HEADER extends DATA_STRUCTURE {
    ORDERID: string;
    ORDER_DESC: string;
    ORDER_TYPE: string;
    ORDER_TYPE_DESC: string;
    LINE_NO: string;
    LINE_DESC: string;
    FLOC: string;
    FLOC_DESC: string;
    EQUIP: string;
    EQUIP_DESC: string;
    PLANT: string;
    BASIC_START_DATE: string;
    BASIC_FINISH_DATE: string;
    OPERATION_NO: string;
    OPERATION_DESC: string;
    CONFIRMATION_NO : number
    EXEC_START_DATE: string;
    EXEC_START_TIME: string;
    EXEC_FIN_DATE: string;
    EXEC_FIN_TIME: string;
    SYSTEM_CONDITION: string;
    TASK_TYPE: string;
    COMPLETION_STATUS: string;
    REASON: string;
    URL:string;
    SHIFT:string;
    PRIORITY:string;
    isCustomMark: boolean;
    isChanged: boolean;
    errMsg : string;
    selReason: string;
    completion: string;
}



