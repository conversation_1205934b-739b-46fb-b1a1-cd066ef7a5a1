import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy } from '@angular/router';

import { IonicModule, IonicRouteStrategy } from '@ionic/angular';

import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { AppConstants } from './Constants/app-constants';
import { LoginParameters, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { NgxPaginationModule } from 'ngx-pagination';
import { FormBuilder, FormsModule, ReactiveFormsModule } from "@angular/forms";
import { IonicSelectableModule } from 'ionic-selectable';
import { TableModule } from 'primeng/table';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule } from '@angular/common/http';
import { NgIdleKeepaliveModule } from '@ng-idle/keepalive';
import { NgIdleModule } from '@ng-idle/core'


@NgModule({
  declarations: [AppComponent],
  entryComponents: [],
  imports: [BrowserModule, IonicModule.forRoot(),NgIdleKeepaliveModule.forRoot(), AppRoutingModule, NgxPaginationModule, FormsModule, ReactiveFormsModule, IonicSelectableModule, TableModule,
    BrowserAnimationsModule, HttpClientModule,],
  providers: [AppConstants, UnviredCordovaSDK, LoginParameters, { provide: RouteReuseStrategy, useClass: IonicRouteStrategy }],

  bootstrap: [AppComponent]
})
export class AppModule { }
